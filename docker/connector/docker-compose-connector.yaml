version: '3.7'

services:
  mariadb:
    image: 'mariadb:11.2.4'
    container_name: unstract-mariadb
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
    env_file:
      - .env

  mysql:
    image: 'mysql:9.0.1'
    container_name: unstract-mysql
    env_file:
      - .env
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  mssql:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: unstract-mssql
    user: "0:0"
    env_file:
      - .env
    ports:
      - "1433:1433"
    volumes:
      - mssql_data:/var/opt/mssql
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools18/bin/sqlcmd -S localhost -U SA -P $$MSSQL_SA_PASSWORD -C -Q 'SELECT 1'"]
      interval: 10s
      retries: 10
      start_period: 10s
      timeout: 3s

  sftp:
    image: atmoz/sftp:debian
    ports:
      - "22:22"
    volumes:
      - sftp_data:/home/<USER>
    command: "${SFTP_USER}:${SFTP_PASS}:${SFTP_USER_ID}::${SFTP_DIR}"

volumes:
  mariadb_data:
  mysql_data:
  mssql_data:
  sftp_data:
