include:
  - docker-compose-dev-essentials.yaml

services:
  # Backend service
  backend:
    image: unstract/backend:${VERSION}
    container_name: unstract-backend
    restart: unless-stopped
    command: --migrate
    ports:
      - "8000:8000"
    env_file:
      - ../backend/.env
    depends_on:
      - db
      - redis
      - rabbitmq
      - reverse-proxy
      - minio
      - createbuckets
      - platform-service
      - prompt-service
      - x2text-service
    volumes:
      - prompt_studio_data:/app/prompt-studio-data
      - ./workflow_data:/data
      - ${TOOL_REGISTRY_CONFIG_SRC_PATH}:/data/tool_registry_config
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-backend
    labels:
      - traefik.enable=true
      - traefik.http.routers.backend.rule=Host(`frontend.unstract.localhost`) && PathPrefix(`/api/v1`, `/deployment`)
    extra_hosts:
      # "host-gateway" is a special string that translates to host docker0 i/f IP.
      - "host.docker.internal:host-gateway"

  # Celery default worker
  worker:
    image: unstract/backend:${VERSION}
    container_name: unstract-worker
    restart: unless-stopped
    entrypoint: .venv/bin/celery
    command: "-A backend worker --loglevel=info -Q celery,celery_api_deployments --autoscale=${WORKER_AUTOSCALE}"
    env_file:
      - ../backend/.env
    depends_on:
      - rabbitmq
      - db
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-worker
    labels:
      - traefik.enable=false
    volumes:
      - ./workflow_data:/data
      - ${TOOL_REGISTRY_CONFIG_SRC_PATH}:/data/tool_registry_config


  # Celery worker for managing logs and periodic tasks
  worker-logging:
    image: unstract/backend:${VERSION}
    container_name: unstract-worker-logging
    restart: unless-stopped
    entrypoint: .venv/bin/celery
    command: "-A backend worker --loglevel=info -Q celery_periodic_logs,celery_log_task_queue --autoscale=${WORKER_LOGGING_AUTOSCALE}"
    env_file:
      - ../backend/.env
    depends_on:
      - rabbitmq
      - db
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-worker-logging
    labels:
      - traefik.enable=false

  # Celery worker for handling file processing tasks
  worker-file-processing:
    image: unstract/backend:${VERSION}
    container_name: unstract-worker-file-processing
    restart: unless-stopped
    entrypoint: .venv/bin/celery
    command: "-A backend.workers.file_processing worker --loglevel=info -Q file_processing,api_file_processing --autoscale=${WORKER_FILE_PROCESSING_AUTOSCALE}"
    env_file:
      - ../backend/.env
    depends_on:
      - rabbitmq
      - db
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-worker-file-processing
    labels:
      - traefik.enable=false
    volumes:
      - ./workflow_data:/data
      - ${TOOL_REGISTRY_CONFIG_SRC_PATH}:/data/tool_registry_config

  worker-file-processing-callback:
    image: unstract/backend:${VERSION}
    container_name: unstract-worker-file-processing-callback
    restart: unless-stopped
    entrypoint: .venv/bin/celery
    command: "-A backend.workers.file_processing_callback worker --loglevel=info -Q file_processing_callback,api_file_processing_callback --autoscale=${WORKER_FILE_PROCESSING_CALLBACK_AUTOSCALE}"
    env_file:
      - ../backend/.env
    depends_on:
      - rabbitmq
      - db
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-worker-file-processing-callback
    labels:
      - traefik.enable=false
    volumes:
      - ./workflow_data:/data
      - ${TOOL_REGISTRY_CONFIG_SRC_PATH}:/data/tool_registry_config

  # Celery Flower
  celery-flower:
    image: unstract/backend:${VERSION}
    container_name: unstract-celery-flower
    restart: unless-stopped
    entrypoint: .venv/bin/celery
    command: "-A backend flower --port=5555 --purge_offline_workers=5"
    env_file:
      - ../backend/.env
    depends_on:
      - worker
      - worker-logging
      - rabbitmq
    labels:
      - traefik.enable=false
    ports:
      - "5555:5555"
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-celery-flower
    volumes:
      - unstract_data:/data
    profiles:
      - optional

  # Celery Beat
  celery-beat:
    image: unstract/backend:${VERSION}
    container_name: unstract-celery-beat
    restart: unless-stopped
    entrypoint: .venv/bin/celery
    command: "-A backend beat --scheduler django_celery_beat.schedulers:DatabaseScheduler -l INFO"
    env_file:
      - ../backend/.env
      - ./essentials.env
    depends_on:
        - db
        - rabbitmq
    environment:
      - ENVIRONMENT=development
      - APPLICATION_NAME=unstract-celery-beat

  # Frontend React app
  frontend:
    image: unstract/frontend:${VERSION}
    container_name: unstract-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    depends_on:
      - backend
      - reverse-proxy
    environment:
      - ENVIRONMENT=development
    labels:
      - traefik.enable=true
      - traefik.http.routers.frontend.rule=Host(`frontend.unstract.localhost`)  && !PathPrefix(`/api/v1`, `/deployment`)

  platform-service:
    image: unstract/platform-service:${VERSION}
    container_name: unstract-platform-service
    restart: unless-stopped
    ports:
      - "3001:3001"
    env_file:
      - ../platform-service/.env
    depends_on:
      - redis
      - db
    labels:
      - traefik.enable=false

  prompt-service:
    image: unstract/prompt-service:${VERSION}
    container_name: unstract-prompt-service
    restart: unless-stopped
    depends_on:
      - db
      - minio
      - createbuckets
      - rabbitmq
    ports:
      - "3003:3003"
    env_file:
      - ../prompt-service/.env
    labels:
      - traefik.enable=false
    extra_hosts:
      # "host-gateway" is a special string that translates to host docker0 i/f IP.
      - "host.docker.internal:host-gateway"

  x2text-service:
    image: unstract/x2text-service:${VERSION}
    container_name: unstract-x2text-service
    restart: unless-stopped
    ports:
      - "3004:3004"
    env_file:
      - ../x2text-service/.env
    depends_on:
      - db
    labels:
      - traefik.enable=false

  runner:
    image: unstract/runner:${VERSION}
    container_name: unstract-runner
    restart: unless-stopped
    ports:
      - 5002:5002
    env_file:
      - ../runner/.env
    volumes:
      - ./workflow_data:/data
      # Docker socket bind mount to spawn tool containers
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis
      - rabbitmq
    labels:
      - traefik.enable=false

volumes:
  prompt_studio_data:
  unstract_data:

networks:
  default:
    # NOTE:
    # Any changes need to be reflected in proxy service too.
    name: unstract-network
