# Refer https://hub.docker.com/_/postgres#:~:text=How%20to%20extend%20this%20image
POSTGRES_USER=unstract_dev
POSTGRES_PASSWORD=unstract_pass
POSTGRES_DB=unstract_db
# Used by db setup script
POSTGRES_SCHEMA=unstract

MINIO_ROOT_USER=minio
MINIO_ROOT_PASSWORD=minio123
MINIO_ACCESS_KEY=minio
MINIO_SECRET_KEY=minio123

# Use encoded password Refer : https://docs.sqlalchemy.org/en/20/core/engines.html#escaping-special-characters-such-as-signs-in-passwords
FLIPT_DB_URL="postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}?sslmode=disable"

QDRANT_USER=unstract_vector_dev
QDRANT_PASS=unstract_vector_pass
QDRANT_DB=unstract_vector_db

# RabbitMQ related envs
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=password
