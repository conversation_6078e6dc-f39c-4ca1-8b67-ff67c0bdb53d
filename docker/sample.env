# Path where public and private tools are registered
# with a YAML and JSONs
TOOL_REGISTRY_CONFIG_SRC_PATH="${PWD}/../unstract/tool-registry/tool_registry_config"

# Celery Autoscaling Configuration
# Specify the maximum and minimum number of concurrent workers for each Celery worker.
# Format: <max_workers>,<min_workers>
# Hint: The max value (max_workers) is related to your CPU resources and the level of concurrency you need.
# Always monitor system performance and adjust the max value as needed.
WORKER_LOGGING_AUTOSCALE=4,1
WORKER_AUTOSCALE=4,1
WORKER_FILE_PROCESSING_AUTOSCALE=4,1
WORKER_FILE_PROCESSING_CALLBACK_AUTOSCALE=4,1
