name: Run tox tests with UV

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review]
    branches: [main]

jobs:
  test:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          # Install a specific version of uv.
          version: "0.6.14"
          python-version: 3.12.9

      - name: Cache tox environments
        uses: actions/cache@v4
        with:
          path: .tox/
          key: ${{ runner.os }}-tox-uv-${{ hashFiles('**/pyproject.toml', '**/tox.ini') }}
          restore-keys: |
            ${{ runner.os }}-tox-uv-

      - name: Install tox with UV
        run: uv pip install tox tox-uv

      - name: Run tox
        id: tox
        run: |
          tox

      - name: Render the report to the PR
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: runner-test-report
          recreate: true
          path: runner-report.md

      - name: Output reports to the job summary when tests fail
        shell: bash
        run: |
          if [ -f "runner-report.md" ]; then
            echo "<details><summary>Runner Test Report</summary>" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            cat "runner-report.md" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "</details>" >> $GITHUB_STEP_SUMMARY
          fi
