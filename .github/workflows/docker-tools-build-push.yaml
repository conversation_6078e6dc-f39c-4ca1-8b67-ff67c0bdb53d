name: Unstract Tools Docker Image Build and Push (Development)

on:
  workflow_dispatch:
    inputs:
      tag:
        description: "Docker image tag"
        required: true
        default: "latest"
      service_name:
        description: "Tool to build"
        required: true
        default: "tool-structure" # Provide a default value
        type: choice
        options: # Define available options
          - tool-classifier
          - tool-structure
          - tool-text-extractor
          - tool-sidecar
      add_latest_tag:
        description: "Also tag as 'latest'"
        required: false
        default: true
        type: boolean

run-name: "[${{ inputs.service_name }}:${{ inputs.tag }}] Docker Image Build and Push (Development)"

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    steps:
      - name: Output Inputs
        run: echo "${{ toJSON(github.event.inputs) }}"

      - name: Checkout code
        uses: actions/checkout@v4

      # Set up QEMU for ARM64 emulation
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: linux/amd64,linux/arm64/v8

      # Set up Docker Buildx for better caching and multi-arch builds
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64/v8

      # Log in to Docker Hub
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      # Define build configuration based on service name
      - name: Set build configuration
        id: build-config
        run: |
          if [ "${{ github.event.inputs.service_name }}" == "tool-classifier" ]; then
            echo "context=./tools/classifier" >> $GITHUB_OUTPUT
            echo "dockerfile=" >> $GITHUB_OUTPUT
          elif [ "${{ github.event.inputs.service_name }}" == "tool-structure" ]; then
            echo "context=./tools/structure" >> $GITHUB_OUTPUT
            echo "dockerfile=" >> $GITHUB_OUTPUT
          elif [ "${{ github.event.inputs.service_name }}" == "tool-text-extractor" ]; then
            echo "context=./tools/text_extractor" >> $GITHUB_OUTPUT
            echo "dockerfile=" >> $GITHUB_OUTPUT
          elif [ "${{ github.event.inputs.service_name }}" == "tool-sidecar" ]; then
            echo "context=." >> $GITHUB_OUTPUT
            echo "dockerfile=docker/dockerfiles/tool-sidecar.Dockerfile" >> $GITHUB_OUTPUT
          fi

      # Determine tags based on inputs
      - name: Determine Docker tags
        id: tags
        run: |
          TAG="${{ github.event.inputs.tag }}"
          TAGS="unstract/${{ github.event.inputs.service_name }}:${TAG}"

          # Add latest tag if checkbox is checked
          if [ "${{ github.event.inputs.add_latest_tag }}" == "true" ]; then
            TAGS="${TAGS}\nunstract/${{ github.event.inputs.service_name }}:latest"
          fi

          echo "tags<<EOF" >> $GITHUB_OUTPUT
          echo -e "$TAGS" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      # Build and push Docker image
      - name: Build and push ${{ github.event.inputs.service_name }}
        uses: docker/build-push-action@v5
        with:
          context: ${{ steps.build-config.outputs.context }}
          file: ${{ steps.build-config.outputs.dockerfile }}
          platforms: linux/amd64,linux/arm64/v8
          push: true
          tags: ${{ steps.tags.outputs.tags }}
          cache-from: type=gha,scope=${{ github.event.inputs.service_name }}
          cache-to: type=gha,mode=max,scope=${{ github.event.inputs.service_name }}
