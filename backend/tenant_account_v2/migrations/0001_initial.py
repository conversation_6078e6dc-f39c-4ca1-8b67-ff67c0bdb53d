# Generated by Django 4.2.1 on 2024-09-25 09:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("account_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="OrganizationMember",
            fields=[
                ("member_id", models.BigAutoField(primary_key=True, serialize=False)),
                ("role", models.Char<PERSON>ield()),
                (
                    "is_login_onboarding_msg",
                    models.<PERSON><PERSON>an<PERSON>ield(
                        db_comment="Flag to indicate whether the onboarding messages are shown",
                        default=True,
                    ),
                ),
                (
                    "is_prompt_studio_onboarding_msg",
                    models.BooleanField(
                        db_comment="Flag to indicate whether the prompt studio messages are shown",
                        default=True,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        default=None,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="organization_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization Member",
                "verbose_name_plural": "Organization Members",
                "db_table": "organization_member",
            },
        ),
        migrations.AddConstraint(
            model_name="organizationmember",
            constraint=models.UniqueConstraint(
                fields=("organization", "user"), name="unique_organization_member"
            ),
        ),
    ]
