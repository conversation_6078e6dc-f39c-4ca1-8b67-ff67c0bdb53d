# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("workflow_v2", "0001_initial"),
        ("account_v2", "0001_initial"),
        ("pipeline_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="APIDeployment",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        db_comment="User-given display name for the API.",
                        default="default api",
                        max_length=30,
                    ),
                ),
                (
                    "description",
                    models.Char<PERSON>ield(
                        blank=True,
                        db_comment="User-given description for the API.",
                        default="",
                        max_length=255,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_comment="Flag indicating whether the API is active or not.",
                        default=True,
                    ),
                ),
                (
                    "api_endpoint",
                    models.CharField(
                        db_comment="URL endpoint for the API deployment.",
                        editable=False,
                        max_length=255,
                        unique=True,
                    ),
                ),
                (
                    "api_name",
                    models.CharField(
                        db_comment="Short name for the API deployment.",
                        default=uuid.uuid4,
                        max_length=30,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="apis_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="apis_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        db_comment="Foreign key reference to the Workflow model.",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="apis",
                        to="workflow_v2.workflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Api Deployment",
                "verbose_name_plural": "Api Deployments",
                "db_table": "api_deployment",
            },
        ),
        migrations.CreateModel(
            name="APIKey",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_comment="Unique identifier for the API key.",
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "api_key",
                    models.UUIDField(
                        db_comment="Actual key UUID.",
                        default=uuid.uuid4,
                        editable=False,
                        unique=True,
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        db_comment="Description of the API key.",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_comment="Flag indicating whether the API key is active or not.",
                        default=True,
                    ),
                ),
                (
                    "api",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the APIDeployment model.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="api_keys",
                        to="api_v2.apideployment",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="api_keys_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="api_keys_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "pipeline",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Pipeline model.",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="pipeline_v2.pipeline",
                    ),
                ),
            ],
            options={
                "verbose_name": "Api Deployment key",
                "verbose_name_plural": "Api Deployment keys",
                "db_table": "api_deployment_key",
            },
        ),
        migrations.AddConstraint(
            model_name="apideployment",
            constraint=models.UniqueConstraint(
                fields=("api_name", "organization"), name="unique_api_name"
            ),
        ),
    ]
