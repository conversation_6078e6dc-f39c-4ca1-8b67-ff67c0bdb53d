# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("workflow_v2", "0001_initial"),
        ("connector_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ToolInstance",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "tool_id",
                    models.CharField(
                        db_comment="Function name of the tool being used", max_length=64
                    ),
                ),
                (
                    "input",
                    models.JSONField(
                        db_comment="Provisional WF input to a tool", null=True
                    ),
                ),
                (
                    "output",
                    models.J<PERSON><PERSON>ield(
                        db_comment="Provisional WF output to a tool", null=True
                    ),
                ),
                ("version", models.CharField(max_length=16)),
                ("metadata", models.J<PERSON><PERSON>ield(db_comment="Stores config for a tool")),
                ("step", models.IntegerField()),
                ("status", models.CharField(default="Ready to start", max_length=32)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tool_instances_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "input_db_connector",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="input_db_connectors",
                        to="connector_v2.connectorinstance",
                    ),
                ),
                (
                    "input_file_connector",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="input_file_connectors",
                        to="connector_v2.connectorinstance",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tool_instances_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "output_db_connector",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="output_db_connectors",
                        to="connector_v2.connectorinstance",
                    ),
                ),
                (
                    "output_file_connector",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="output_file_connectors",
                        to="connector_v2.connectorinstance",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tool_instances",
                        to="workflow_v2.workflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Tool Instance",
                "verbose_name_plural": "Tool Instances",
                "db_table": "tool_instance",
            },
        ),
    ]
