# Generated by Django 4.2.1 on 2025-04-08 13:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0012_remove_wf_exec_with_invalid_wf_references"),
    ]

    operations = [
        migrations.AlterField(
            model_name="workflowexecution",
            name="workflow_id",
            field=models.ForeignKey(
                db_column="workflow_id",
                db_comment="Workflow to be executed",
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="workflow_executions",
                to="workflow_v2.workflow",
            ),
        ),
        # Rename the field in Django's ORM (doesn't affect the database column name)
        migrations.RenameField(
            model_name="workflowexecution",
            old_name="workflow_id",
            new_name="workflow",
        ),
    ]
