# Generated by Django 4.2.1 on 2025-03-12 08:34

from typing import Any

from django.db import migrations


def update_total_files(apps: Any, schema_editor: Any) -> None:
    """Update total_files column in WorkflowExecution model

    Obtained by counting the number of associated WorkflowFileExecution records
    """
    WorkflowExecution = apps.get_model(  # pylint: disable=invalid-name # NOSONAR
        "workflow_v2", "WorkflowExecution"
    )

    # Update total_files for older executions with a count of zero
    for execution in WorkflowExecution.objects.filter(total_files=0):
        count = execution.file_executions.count()
        execution.total_files = count
        execution.save(update_fields=["total_files"])


def reverse_update_total_files(apps: Any, schema_editor: Any) -> None:
    """Reverse the update by setting total_files back to zero for all file executions"""
    WorkflowExecution = apps.get_model(  # pylint: disable=invalid-name # NOSONAR
        "workflow_v2", "WorkflowExecution"
    )

    WorkflowExecution.objects.update(total_files=0)


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0008_workflowexecution_total_files_and_more"),
    ]

    operations = [
        migrations.RunPython(update_total_files, reverse_update_total_files),
    ]
