# Generated by Django 4.2.1 on 2025-02-12 08:07

from django.db import migrations
from django.db.models import ExpressionWrapper, F, FloatField, Func


def update_execution_time_forward(apps, schema_editor):
    """Calculate execution_time as modified_at - created_at in seconds (rounded to 3 decimal places)."""
    WorkflowExecution = apps.get_model("workflow_v2", "WorkflowExecution")

    # Calculate execution_time as (modified_at - created_at) in seconds
    execution_time_expr = ExpressionWrapper(
        Func(
            Func(
                F("modified_at") - F("created_at"),
                function="EXTRACT",
                template="EXTRACT(EPOCH FROM %(expressions)s)",
            ),
            3,  # Round to 3 decimal places
            function="ROUND",
        ),
        output_field=FloatField(),
    )

    WorkflowExecution.objects.update(execution_time=execution_time_expr)


# NOTE: The reverse migration does affect unintended rows which were not affected during
# the forward migration - however every execution time gets computed as expected
# and this doesn't warrant maintaining a temp table / additional column to
# track the affected rows.
def update_execution_time_backward(apps, schema_editor):
    """Reset execution_time to 0.0 for rollback."""
    WorkflowExecution = apps.get_model("workflow_v2", "WorkflowExecution")
    WorkflowExecution.objects.update(execution_time=0.0)


class Migration(migrations.Migration):
    dependencies = [
        (
            "workflow_v2",
            "0006_workflowexecution_workflow_ex_workflo_5942c9_idx_and_more",
        ),
    ]

    operations = [
        migrations.RunPython(
            update_execution_time_forward, update_execution_time_backward
        ),
    ]
