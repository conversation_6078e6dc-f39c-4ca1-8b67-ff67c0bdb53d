# Generated by Django 4.2.1 on 2025-03-18 12:39

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0009_update_total_files"),
    ]

    operations = [
        migrations.AddField(
            model_name="executionlog",
            name="wf_execution",
            field=models.ForeignKey(
                blank=True,
                db_comment="Foreign key from WorkflowExecution model",
                editable=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="execution_logs",
                to="workflow_v2.workflowexecution",
            ),
        ),
        migrations.AlterField(
            model_name="executionlog",
            name="execution_id",
            field=models.UUIDField(
                db_comment="Execution ID (deprecated, refer wf_execution instead)",
                editable=False,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="filehistory",
            name="status",
            field=models.TextField(
                choices=[
                    ("PENDING", "Pending"),
                    ("EXECUTING", "Executing"),
                    ("COMPLETED", "Completed"),
                    ("STOPPED", "Stopped"),
                    ("ERROR", "Error"),
                ],
                db_comment="Latest status of execution",
            ),
        ),
        migrations.AlterField(
            model_name="workflowexecution",
            name="status",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("EXECUTING", "Executing"),
                    ("COMPLETED", "Completed"),
                    ("STOPPED", "Stopped"),
                    ("ERROR", "Error"),
                ],
                db_comment="Current status of the execution",
            ),
        ),
    ]
