# Generated by Django 4.2.1 on 2025-04-02 13:22

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0010_executionlog_wf_execution_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="filehistory",
            name="unique_workflow_cacheKey",
        ),
        migrations.AddField(
            model_name="filehistory",
            name="provider_file_uuid",
            field=models.CharField(
                db_comment="Unique identifier assigned by the file storage provider",
                max_length=64,
                null=True,
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                condition=models.Q(("cache_key__isnull", False)),
                fields=("workflow", "cache_key"),
                name="unique_workflow_cacheKey",
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                condition=models.Q(("provider_file_uuid__isnull", False)),
                fields=("workflow", "provider_file_uuid"),
                name="unique_workflow_providerFileUUID",
            ),
        ),
    ]
