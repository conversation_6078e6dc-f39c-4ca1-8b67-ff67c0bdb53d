# Generated by Django 4.2.1 on 2025-06-18 04:45

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0013_remove_workflowexecution_workflow_id_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="filehistory",
            name="unique_workflow_cacheKey",
        ),
        migrations.RemoveConstraint(
            model_name="filehistory",
            name="unique_workflow_providerFileUUID",
        ),
        migrations.AddField(
            model_name="filehistory",
            name="file_path",
            field=models.CharField(
                db_comment="Full Path of the file", max_length=1000, null=True
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("cache_key__isnull", False), ("file_path__isnull", True)
                ),
                fields=("workflow", "cache_key"),
                name="unique_workflow_cacheKey",
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("file_path__isnull", True), ("provider_file_uuid__isnull", False)
                ),
                fields=("workflow", "provider_file_uuid"),
                name="unique_workflow_providerFileUUID",
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("cache_key__isnull", False), ("file_path__isnull", False)
                ),
                fields=("workflow", "cache_key", "file_path"),
                name="unique_workflow_cacheKey_with_filePath",
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("file_path__isnull", False), ("provider_file_uuid__isnull", False)
                ),
                fields=("workflow", "provider_file_uuid", "file_path"),
                name="unique_workflow_providerFileUUID_with_filePath",
            ),
        ),
    ]
