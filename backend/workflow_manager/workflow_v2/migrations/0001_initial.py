# Generated by Django 4.2.1 on 2024-07-29 09:16

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("account_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExecutionLog",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "execution_id",
                    models.UUIDField(db_comment="Execution ID", editable=False),
                ),
                ("data", models.JSONField(db_comment="Execution log data")),
                (
                    "event_time",
                    models.DateTimeField(db_comment="Execution log event time"),
                ),
            ],
            options={
                "verbose_name": "Execution Log",
                "verbose_name_plural": "Execution Logs",
                "db_table": "execution_log",
            },
        ),
        migrations.CreateModel(
            name="WorkflowExecution",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "pipeline_id",
                    models.UUIDField(
                        db_comment="ID of the associated pipeline, if applicable",
                        editable=False,
                        null=True,
                    ),
                ),
                (
                    "task_id",
                    models.UUIDField(
                        db_comment="task id of asynchronous execution",
                        editable=False,
                        null=True,
                    ),
                ),
                (
                    "workflow_id",
                    models.UUIDField(
                        db_comment="Id of workflow to be executed", editable=False
                    ),
                ),
                (
                    "execution_mode",
                    models.CharField(
                        choices=[
                            ("INSTANT", "will be executed immediately"),
                            ("QUEUE", "will be placed in a queue"),
                        ],
                        db_comment="Mode of execution",
                    ),
                ),
                (
                    "execution_method",
                    models.CharField(
                        choices=[
                            ("DIRECT", " Execution triggered manually"),
                            ("SCHEDULED", "Scheduled execution"),
                        ],
                        db_comment="Method of execution",
                    ),
                ),
                (
                    "execution_type",
                    models.CharField(
                        choices=[
                            ("COMPLETE", "For complete execution"),
                            ("STEP", "For step-by-step execution "),
                        ],
                        db_comment="Type of execution",
                    ),
                ),
                (
                    "execution_log_id",
                    models.CharField(
                        db_comment="Execution log events Id", default="", editable=False
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        db_comment="Current status of execution", default=""
                    ),
                ),
                (
                    "error_message",
                    models.CharField(
                        blank=True,
                        db_comment="Details of encountered errors",
                        default="",
                        max_length=256,
                    ),
                ),
                (
                    "attempts",
                    models.IntegerField(db_comment="number of attempts taken", default=0),
                ),
                (
                    "execution_time",
                    models.FloatField(db_comment="execution time in seconds", default=0),
                ),
            ],
            options={
                "verbose_name": "Workflow Execution",
                "verbose_name_plural": "Workflow Executions",
                "db_table": "workflow_execution",
            },
        ),
        migrations.CreateModel(
            name="Workflow",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("prompt_name", models.CharField(default="", max_length=32)),
                ("description", models.TextField(default="", max_length=490)),
                ("workflow_name", models.CharField(max_length=128)),
                ("prompt_text", models.TextField(default="")),
                ("is_active", models.BooleanField(default=False)),
                ("status", models.CharField(default="", max_length=16)),
                ("llm_response", models.TextField()),
                (
                    "deployment_type",
                    models.CharField(
                        choices=[
                            ("DEFAULT", "Not ready yet"),
                            ("ETL", "ETL pipeline"),
                            ("TASK", "TASK pipeline"),
                            ("API", "API deployment"),
                            ("APP", "App deployment"),
                        ],
                        db_comment="Type of workflow deployment",
                        default="DEFAULT",
                    ),
                ),
                (
                    "source_settings",
                    models.JSONField(
                        db_comment="Settings for the Source module", null=True
                    ),
                ),
                (
                    "destination_settings",
                    models.JSONField(
                        db_comment="Settings for the Destination module", null=True
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="workflows_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="workflows_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "workflow_owner",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="workflows_owned",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow",
                "verbose_name_plural": "Workflows",
                "db_table": "workflow",
            },
        ),
        migrations.CreateModel(
            name="FileHistory",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cache_key",
                    models.CharField(
                        db_comment="Hash value of file contents, WF and tool modified times",
                        max_length=64,
                    ),
                ),
                (
                    "status",
                    models.TextField(
                        choices=[
                            ("PENDING", "PENDING"),
                            ("INITIATED", "INITIATED"),
                            ("QUEUED", "QUEUED"),
                            ("READY", "READY"),
                            ("EXECUTING", "EXECUTING"),
                            ("COMPLETED", "COMPLETED"),
                            ("STOPPED", "STOPPED"),
                            ("ERROR", "ERROR"),
                        ],
                        db_comment="Latest status of execution",
                    ),
                ),
                (
                    "error",
                    models.TextField(blank=True, db_comment="Error message", default=""),
                ),
                (
                    "result",
                    models.TextField(blank=True, db_comment="Result from execution"),
                ),
                (
                    "metadata",
                    models.TextField(blank=True, db_comment="MetaData from execution"),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="file_histories",
                        to="workflow_v2.workflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "File History",
                "verbose_name_plural": "File Histories",
                "db_table": "file_history",
            },
        ),
        migrations.AddConstraint(
            model_name="workflow",
            constraint=models.UniqueConstraint(
                fields=("workflow_name", "organization"), name="unique_workflow_name"
            ),
        ),
        migrations.AddConstraint(
            model_name="filehistory",
            constraint=models.UniqueConstraint(
                fields=("workflow", "cache_key"), name="unique_workflow_cacheKey"
            ),
        ),
    ]
