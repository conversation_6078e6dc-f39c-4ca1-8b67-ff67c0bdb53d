# Generated by Django 4.2.1 on 2025-02-17 10:15

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0007_update_execution_time"),
    ]

    operations = [
        migrations.AddField(
            model_name="workflowexecution",
            name="total_files",
            field=models.PositiveIntegerField(
                db_comment="Number of files to process",
                default=0,
                verbose_name="Total files",
            ),
        ),
        migrations.AlterField(
            model_name="filehistory",
            name="status",
            field=models.TextField(
                choices=[
                    ("PENDING", "Pending"),
                    ("INITIATED", "Initiated"),
                    ("QUEUED", "Queued"),
                    ("READY", "Ready"),
                    ("EXECUTING", "Executing"),
                    ("COMPLETED", "Completed"),
                    ("STOPPED", "Stopped"),
                    ("ERROR", "Error"),
                ],
                db_comment="Latest status of execution",
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="workflowexecution",
            name="result_acknowledged",
            field=models.<PERSON><PERSON>anField(
                db_comment="To track if result is acknowledged by user - used mainly by API deployments",
                default=False,
            ),
        ),
        migrations.AlterField(
            model_name="workflowexecution",
            name="status",
            field=models.CharField(
                choices=[
                    ("PENDING", "Pending"),
                    ("INITIATED", "Initiated"),
                    ("QUEUED", "Queued"),
                    ("READY", "Ready"),
                    ("EXECUTING", "Executing"),
                    ("COMPLETED", "Completed"),
                    ("STOPPED", "Stopped"),
                    ("ERROR", "Error"),
                ],
                db_comment="Current status of the execution",
            ),
        ),
    ]
