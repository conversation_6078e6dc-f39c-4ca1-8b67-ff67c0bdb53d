# Generated by Django 4.2.1 on 2025-04-08 13:14

import logging
from typing import Any

from django.db import migrations

logger = logging.getLogger(__name__)


def remove_execution_of_deleted_wfs(apps: Any, schema_editor: Any):
    """Removes records from workflow_execution which do not have an associated workflow."""
    # Get the models from the migration state
    WorkflowExecution = apps.get_model(  # pylint: disable=invalid-name # NOSONAR
        "workflow_v2", "WorkflowExecution"
    )
    Workflow = apps.get_model(  # pylint: disable=invalid-name # NOSONAR
        "workflow_v2", "Workflow"
    )

    # Get all valid workflow IDs
    valid_workflow_ids = set(Workflow.objects.values_list("id", flat=True))

    # Find invalid workflow executions
    invalid_executions = WorkflowExecution.objects.filter(
        workflow_id__isnull=False
    ).exclude(workflow_id__in=valid_workflow_ids)

    invalid_count = invalid_executions.count()

    if invalid_count > 0:
        logger.warning(
            f"Deleting '{invalid_count}' workflow executions with "
            "invalid workflow references"
        )

        # Delete in batches to avoid long-running transactions
        batch_size = 1000
        deleted_total = 0

        while True:
            # Get a batch of IDs to delete
            batch_ids = list(invalid_executions[:batch_size].values_list("id", flat=True))

            if not batch_ids:
                break

            # Delete this batch
            deleted_count = WorkflowExecution.objects.filter(id__in=batch_ids).delete()[0]
            deleted_total += deleted_count

            # Includes deletion of related models also
            logger.info(f"Deleted {deleted_count} records (total: {deleted_total})")

            # If we deleted fewer than batch_size, we're done
            if deleted_count < batch_size:
                break


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0011_remove_filehistory_unique_workflow_cachekey_and_more"),
    ]

    operations = [
        migrations.RunPython(
            remove_execution_of_deleted_wfs, reverse_code=migrations.RunPython.noop
        ),
    ]
