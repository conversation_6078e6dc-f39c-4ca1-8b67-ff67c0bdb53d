# Generated by Django 4.2.1 on 2025-02-17 10:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0008_workflowexecution_total_files_and_more"),
        ("file_execution", "0002_rename_status_and_update_exec_time"),
    ]

    operations = [
        migrations.AlterField(
            model_name="workflowfileexecution",
            name="status",
            field=models.TextField(
                choices=[
                    ("PENDING", "Pending"),
                    ("INITIATED", "Initiated"),
                    ("QUEUED", "Queued"),
                    ("READY", "Ready"),
                    ("EXECUTING", "Executing"),
                    ("COMPLETED", "Completed"),
                    ("STOPPED", "Stopped"),
                    ("ERROR", "Error"),
                ],
                db_comment="Current status of the execution",
            ),
        ),
        migrations.AlterField(
            model_name="workflowfileexecution",
            name="workflow_execution",
            field=models.ForeignKey(
                db_comment="Foreign key from WorkflowExecution model",
                editable=False,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="file_executions",
                to="workflow_v2.workflowexecution",
            ),
        ),
    ]
