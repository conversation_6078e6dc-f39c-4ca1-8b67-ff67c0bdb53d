# Generated by Django 4.2.1 on 2025-04-02 13:22

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("file_execution", "0004_alter_workflowfileexecution_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="workflowfileexecution",
            name="fs_metadata",
            field=models.J<PERSON><PERSON>ield(
                db_comment="Complete metadata of the file retrieved from the file system.",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="workflowfileexecution",
            name="provider_file_uuid",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                db_comment="Unique identifier assigned by the file storage provider",
                max_length=64,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="workflowfileexecution",
            name="file_hash",
            field=models.Char<PERSON>ield(
                db_comment="Hash of the file content", max_length=64, null=True
            ),
        ),
        migrations.AddIndex(
            model_name="workflowfileexecution",
            index=models.Index(
                fields=["workflow_execution", "provider_file_uuid"],
                name="workflow_exec_p_uuid_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="workflowfileexecution",
            constraint=models.UniqueConstraint(
                fields=("workflow_execution", "provider_file_uuid", "file_path"),
                name="unique_workflow_provider_uuid_path",
            ),
        ),
    ]
