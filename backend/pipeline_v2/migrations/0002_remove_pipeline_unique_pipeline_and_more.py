# Generated by Django 4.2.1 on 2024-10-04 10:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("pipeline_v2", "0001_initial"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="pipeline",
            name="unique_pipeline",
        ),
        migrations.AlterField(
            model_name="pipeline",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="pipelines_created",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="pipeline",
            name="modified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="pipelines_modified",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="pipeline",
            name="pipeline_name",
            field=models.Char<PERSON>ield(default="", max_length=32),
        ),
        migrations.AlterField(
            model_name="pipeline",
            name="workflow",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pipelines",
                to="workflow_v2.workflow",
            ),
        ),
        migrations.AddConstraint(
            model_name="pipeline",
            constraint=models.UniqueConstraint(
                fields=("id", "pipeline_type"), name="unique_pipeline_entity"
            ),
        ),
        migrations.AddConstraint(
            model_name="pipeline",
            constraint=models.UniqueConstraint(
                fields=("pipeline_name", "organization"), name="unique_pipeline_name"
            ),
        ),
    ]
