# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("tenant_account_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("account_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="AdapterInstance",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_comment="Unique identifier for the Adapter Instance",
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "adapter_name",
                    models.TextField(
                        db_comment="Name of the Adapter Instance", max_length=128
                    ),
                ),
                (
                    "adapter_id",
                    models.<PERSON>r<PERSON><PERSON>(
                        db_comment="Unique identifier of the Adapter",
                        default="",
                        max_length=128,
                    ),
                ),
                (
                    "adapter_metadata",
                    models.J<PERSON><PERSON>ield(
                        db_column="adapter_metadata",
                        db_comment="JSON adapter metadata submitted by the user",
                        default=dict,
                    ),
                ),
                ("adapter_metadata_b", models.BinaryField(null=True)),
                (
                    "adapter_type",
                    models.CharField(
                        choices=[
                            ("UNKNOWN", "UNKNOWN"),
                            ("LLM", "LLM"),
                            ("EMBEDDING", "EMBEDDING"),
                            ("VECTOR_DB", "VECTOR_DB"),
                            ("OCR", "OCR"),
                            ("X2TEXT", "X2TEXT"),
                        ],
                        db_comment="Type of adapter LLM/EMBEDDING/VECTOR_DB",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_comment="Is the adapter instance currently being used",
                        default=False,
                    ),
                ),
                (
                    "shared_to_org",
                    models.BooleanField(
                        db_comment="Is the adapter shared to entire org", default=False
                    ),
                ),
                (
                    "is_friction_less",
                    models.BooleanField(
                        db_comment="Was the adapter created through frictionless onboarding",
                        default=False,
                    ),
                ),
                (
                    "is_usable",
                    models.BooleanField(db_comment="Is the Adpater Usable", default=True),
                ),
                ("description", models.TextField(blank=True, default=None, null=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="adapters_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="adapters_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "shared_users",
                    models.ManyToManyField(
                        related_name="shared_adapters_instance",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "adapter instance",
                "verbose_name_plural": "adapter instances",
                "db_table": "adapter_instance",
            },
        ),
        migrations.CreateModel(
            name="UserDefaultAdapter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "default_embedding_adapter",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_default_embedding_adapter",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "default_llm_adapter",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_default_llm_adapter",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "default_vector_db_adapter",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_default_vector_db_adapter",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "default_x2text_adapter",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_default_x2text_adapter",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "organization_member",
                    models.OneToOneField(
                        db_comment="Foreign key reference to the OrganizationMember model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="default_adapters",
                        to="tenant_account_v2.organizationmember",
                    ),
                ),
            ],
            options={
                "verbose_name": "Default Adapter for Organization User",
                "verbose_name_plural": "Default Adapters for Organization Users",
                "db_table": "default_organization_user_adapter",
            },
        ),
        migrations.AddConstraint(
            model_name="adapterinstance",
            constraint=models.UniqueConstraint(
                fields=("adapter_name", "adapter_type", "organization"),
                name="unique_organization_adapter",
            ),
        ),
    ]
