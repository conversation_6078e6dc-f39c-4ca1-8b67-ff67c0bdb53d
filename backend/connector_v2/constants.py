class ConnectorInstanceKey:
    CONNECTOR_ID = "connector_id"
    CONNECTOR_NAME = "connector_name"
    CONNECTOR_TYPE = "connector_type"
    CONNECTOR_MODE = "connector_mode"
    CONNECTOR_VERSION = "connector_version"
    CONNECTOR_AUTH = "connector_auth"
    CONNECTOR_METADATA = "connector_metadata"
    CONNECTOR_EXISTS = "Connector with this configuration already exists in this project."
    DUPLICATE_API = "It appears that a duplicate call may have been made."


class ConnectorInstanceConstant:
    USER_STORAGE = "User Storage"
