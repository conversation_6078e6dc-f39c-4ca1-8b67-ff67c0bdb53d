# Generated by Django 4.2.1 on 2025-07-16 10:26

import django.db.models.deletion
import utils.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("workflow_v2", "0015_executionlog_idx_wf_execution_event_time_and_more"),
        ("connector_v2", "0001_initial"),
    ]

    operations = [
        migrations.AlterField(
            model_name="connectorinstance",
            name="connector_metadata",
            field=utils.fields.EncryptedBinaryField(null=True),
        ),
        migrations.AlterField(
            model_name="connectorinstance",
            name="connector_type",
            field=models.CharField(
                choices=[("INPUT", "Input"), ("OUTPUT", "Output")], null=True
            ),
        ),
        migrations.AlterField(
            model_name="connectorinstance",
            name="workflow",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="connector_workflow",
                to="workflow_v2.workflow",
            ),
        ),
    ]
