# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("workflow_v2", "0001_initial"),
        ("connector_auth_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("account_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ConnectorInstance",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("connector_name", models.TextField(max_length=128)),
                ("connector_id", models.Char<PERSON>ield(default="", max_length=128)),
                ("connector_metadata", models.BinaryField(null=True)),
                ("connector_version", models.Char<PERSON>ield(default="", max_length=64)),
                (
                    "connector_type",
                    models.CharField(choices=[("INPUT", "Input"), ("OUTPUT", "Output")]),
                ),
                (
                    "connector_mode",
                    models.CharField(
                        choices=[(0, "UNKNOWN"), (1, "FILE_SYSTEM"), (2, "DATABASE")],
                        db_comment="0: UNKNOWN, 1: FILE_SYSTEM, 2: DATABASE",
                        default=0,
                    ),
                ),
                (
                    "connector_auth",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="connector_instances",
                        to="connector_auth_v2.connectorauth",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="connectors_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="connectors_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="connector_workflow",
                        to="workflow_v2.workflow",
                    ),
                ),
            ],
            options={
                "verbose_name": "Connector Instance",
                "verbose_name_plural": "Connector Instances",
                "db_table": "connector_instance",
            },
        ),
        migrations.AddConstraint(
            model_name="connectorinstance",
            constraint=models.UniqueConstraint(
                fields=("connector_name", "workflow", "connector_type"),
                name="unique_workflow_connector",
            ),
        ),
    ]
