# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("prompt_studio_core_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentManager",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "document_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "document_name",
                    models.CharField(
                        db_comment="Field to store the document name", editable=False
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="document_managers_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="document_managers_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tool",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_managers",
                        to="prompt_studio_core_v2.customtool",
                    ),
                ),
            ],
            options={
                "verbose_name": "Document Manager",
                "verbose_name_plural": "Document Managers",
                "db_table": "document_manager",
            },
        ),
        migrations.AddConstraint(
            model_name="documentmanager",
            constraint=models.UniqueConstraint(
                fields=("document_name", "tool"), name="unique_document_name_tool_index"
            ),
        ),
    ]
