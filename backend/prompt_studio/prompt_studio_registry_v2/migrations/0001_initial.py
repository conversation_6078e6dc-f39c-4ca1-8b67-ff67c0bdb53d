# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models

import prompt_studio.prompt_studio_registry_v2.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("prompt_studio_core_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("account_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PromptStudioRegistry",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "prompt_registry_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(default="", editable=False)),
                ("description", models.CharField(default="", editable=False)),
                (
                    "tool_property",
                    prompt_studio.prompt_studio_registry_v2.fields.ToolPropertyJSONField(
                        db_column="tool_property",
                        db_comment="PROPERTIES of the tool",
                        default=dict,
                    ),
                ),
                (
                    "tool_spec",
                    prompt_studio.prompt_studio_registry_v2.fields.ToolSpecJSONField(
                        db_column="tool_spec",
                        db_comment="SPEC of the tool",
                        default=dict,
                    ),
                ),
                (
                    "tool_metadata",
                    prompt_studio.prompt_studio_registry_v2.fields.ToolMetadataJSONField(
                        db_column="tool_metadata",
                        db_comment="Metadata from Prompt Studio",
                        default=dict,
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        db_comment="Tool icon in svg format", editable=False
                    ),
                ),
                ("url", models.CharField(editable=False)),
                (
                    "shared_to_org",
                    models.BooleanField(
                        db_comment="Is the exported tool shared with entire org",
                        default=False,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="prompt_registries_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "custom_tool",
                    models.OneToOneField(
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prompt_studio_registries",
                        to="prompt_studio_core_v2.customtool",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="prompt_registries_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "shared_users",
                    models.ManyToManyField(
                        related_name="prompt_registries", to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                "verbose_name": "Prompt Studio Registry",
                "verbose_name_plural": "Prompt Studio Registries",
                "db_table": "prompt_studio_registry",
            },
        ),
    ]
