# Generated by Django 4.2.1 on 2025-08-01 10:04

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("prompt_profile_manager_v2", "0002_alter_profilemanager_retrieval_strategy"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="profilemanager",
            name="retrieval_strategy",
            field=models.TextField(
                blank=True,
                choices=[
                    ("simple", "Simple retrieval"),
                    ("subquestion", "Subquestion retrieval"),
                    ("fusion", "Fusion retrieval"),
                    ("recursive", "Recursive retrieval"),
                    ("router", "Router retrieval"),
                    ("keyword_table", "Keyword table retrieval"),
                    ("automerging", "Auto-merging retrieval"),
                ],
                db_comment="Field to store the retrieval strategy for prompts",
                default="simple",
            ),
        ),
    ]
