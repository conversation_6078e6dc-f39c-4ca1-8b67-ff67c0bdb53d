# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("adapter_processor_v2", "0001_initial"),
        ("prompt_studio_core_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ProfileManager",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "profile_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("profile_name", models.TextField()),
                ("chunk_size", models.IntegerField(blank=True, null=True)),
                ("chunk_overlap", models.IntegerField(blank=True, null=True)),
                ("reindex", models.BooleanField(default=False)),
                (
                    "retrieval_strategy",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("simple", "Simple retrieval"),
                            ("subquestion", "Subquestion retrieval"),
                        ],
                        db_comment="Field to store the retrieval strategy for prompts",
                    ),
                ),
                (
                    "similarity_top_k",
                    models.IntegerField(
                        blank=True,
                        db_comment="Field to store number of top embeddings to take into context",
                        null=True,
                    ),
                ),
                (
                    "section",
                    models.TextField(
                        blank=True,
                        db_comment="Field to store limit to section",
                        null=True,
                    ),
                ),
                (
                    "is_default",
                    models.BooleanField(
                        db_comment="Default LLM Profile used in prompt", default=False
                    ),
                ),
                (
                    "is_summarize_llm",
                    models.BooleanField(
                        db_comment="Default LLM Profile used for summarizing",
                        default=False,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="profile_managers_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "embedding_model",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="profiles_embedding_model",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "llm",
                    models.ForeignKey(
                        db_comment="Field to store the LLM chosen by the user",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="profiles_llm",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="profile_managers_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "prompt_studio_tool",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile_managers",
                        to="prompt_studio_core_v2.customtool",
                    ),
                ),
                (
                    "vector_store",
                    models.ForeignKey(
                        db_comment="Field to store the chosen vector store.",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="profiles_vector_store",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "x2text",
                    models.ForeignKey(
                        db_comment="Field to store the X2Text Adapter chosen by the user",
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="profiles_x2text",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
            ],
            options={
                "verbose_name": "Profile Manager",
                "verbose_name_plural": "Profile Managers",
                "db_table": "profile_manager",
            },
        ),
        migrations.AddConstraint(
            model_name="profilemanager",
            constraint=models.UniqueConstraint(
                fields=("prompt_studio_tool", "profile_name"),
                name="unique_prompt_studio_tool_profile_name_index",
            ),
        ),
    ]
