# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("prompt_studio_core_v2", "0001_initial"),
        ("prompt_studio_document_manager_v2", "0001_initial"),
        ("prompt_studio_v2", "0001_initial"),
        ("prompt_profile_manager_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PromptStudioOutputManager",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "prompt_output_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "output",
                    models.CharField(
                        blank=True, db_comment="Field to store output", null=True
                    ),
                ),
                (
                    "context",
                    models.TextField(
                        blank=True, db_comment="Field to store chunks used", null=True
                    ),
                ),
                (
                    "challenge_data",
                    models.JSONField(
                        blank=True,
                        db_comment="Field to store challenge data",
                        null=True,
                    ),
                ),
                (
                    "eval_metrics",
                    models.JSONField(
                        db_column="eval_metrics",
                        db_comment="Field to store the evaluation metrics",
                        default=list,
                    ),
                ),
                (
                    "is_single_pass_extract",
                    models.BooleanField(
                        db_comment="Is the single pass extraction mode active",
                        default=False,
                    ),
                ),
                ("run_id", models.UUIDField(default=uuid.uuid4, editable=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="prompt_studio_outputs_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "document_manager",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prompt_studio_outputs",
                        to="prompt_studio_document_manager_v2.documentmanager",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="prompt_studio_outputs_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "profile_manager",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prompt_studio_outputs",
                        to="prompt_profile_manager_v2.profilemanager",
                    ),
                ),
                (
                    "prompt_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prompt_studio_outputs",
                        to="prompt_studio_v2.toolstudioprompt",
                    ),
                ),
                (
                    "tool_id",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prompt_studio_outputs",
                        to="prompt_studio_core_v2.customtool",
                    ),
                ),
            ],
            options={
                "verbose_name": "Prompt Studio Output Manager",
                "verbose_name_plural": "Prompt Studio Output Managers",
                "db_table": "prompt_studio_output_manager",
            },
        ),
        migrations.AddConstraint(
            model_name="promptstudiooutputmanager",
            constraint=models.UniqueConstraint(
                fields=(
                    "prompt_id",
                    "document_manager",
                    "profile_manager",
                    "tool_id",
                    "is_single_pass_extract",
                ),
                name="unique_prompt_output_index",
            ),
        ),
    ]
