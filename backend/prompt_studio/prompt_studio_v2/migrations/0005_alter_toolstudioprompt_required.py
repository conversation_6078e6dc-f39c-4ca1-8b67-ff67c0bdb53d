# Generated by Django 4.2.1 on 2024-12-20 06:35

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("prompt_studio_v2", "0004_alter_toolstudioprompt_required"),
    ]

    operations = [
        migrations.AlterField(
            model_name="toolstudioprompt",
            name="required",
            field=models.CharField(
                blank=True,
                choices=[("all", "All values required"), ("any", "Any value required")],
                db_comment="Field to store weather the values all values or any         values required. This is used for HQR, based on the value approve or finish         review",
                default=None,
                null=True,
            ),
        ),
    ]
