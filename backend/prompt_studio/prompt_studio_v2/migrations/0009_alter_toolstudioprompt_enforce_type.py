# Generated by Django 4.2.1 on 2025-04-28 05:49

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("prompt_studio_v2", "0008_alter_toolstudioprompt_enforce_type"),
    ]

    operations = [
        migrations.AlterField(
            model_name="toolstudioprompt",
            name="enforce_type",
            field=models.TextField(
                blank=True,
                choices=[
                    ("text", "Response sent as Text"),
                    ("number", "Response sent as number"),
                    ("email", "Response sent as email"),
                    ("date", "Response sent as date"),
                    ("boolean", "Response sent as boolean"),
                    ("json", "Response sent as json"),
                    (
                        "line-item",
                        "Response sent as line-item which is large a JSON output. If extraction stopped due to token limitation, we try to continue extraction from where it stopped",
                    ),
                ],
                db_comment="Field to store the type in             which the response to be returned.",
                default="text",
            ),
        ),
    ]
