# Generated by Django 4.2.1 on 2025-04-28 06:00

from typing import Any

from django.db import migrations


def update_text_enforce_type(apps, schema_editor: Any) -> None:
    prompt = apps.get_model("prompt_studio_v2", "ToolStudioPrompt")
    prompt.objects.filter(enforce_type="Text").update(enforce_type="text")


def revert_update_text_enforce_type(apps, schema_editor: Any) -> None:
    prompt = apps.get_model("prompt_studio_v2", "ToolStudioPrompt")
    prompt.objects.filter(enforce_type="text").update(enforce_type="Text")


class Migration(migrations.Migration):
    dependencies = [
        ("prompt_studio_v2", "0009_alter_toolstudioprompt_enforce_type"),
    ]

    operations = [
        migrations.RunPython(
            update_text_enforce_type, reverse_code=revert_update_text_enforce_type
        ),
    ]
