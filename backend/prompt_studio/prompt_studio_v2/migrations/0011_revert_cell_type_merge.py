# Generated by Django 4.2.1 on 2025-05-09 14:26

from django.db import migrations


def revert_enforce_type_updates(apps, schema_editor):
    prompt = apps.get_model("prompt_studio_v2", "ToolStudioPrompt")

    # Revert json -> line-item for records where is_line_item is True
    prompt.objects.filter(enforce_type="json", has_line_item_history=True).update(
        enforce_type="line-item"
    )

    # Revert line-item -> table
    prompt.objects.filter(enforce_type="line-item").update(enforce_type="table")


def re_apply_enforce_type_updates(apps, schema_editor):
    prompt = apps.get_model("prompt_studio_v2", "ToolStudioPrompt")

    # Apply line-item -> json for records with history
    prompt.objects.filter(enforce_type="line-item", has_line_item_history=True).update(
        enforce_type="json"
    )

    # Apply table -> line-item for all
    prompt.objects.filter(enforce_type="table").update(enforce_type="line-item")


class Migration(migrations.Migration):
    dependencies = [
        ("prompt_studio_v2", "0010_update_enforce_type_text"),
    ]

    operations = [
        migrations.RunPython(
            code=revert_enforce_type_updates,
            reverse_code=re_apply_enforce_type_updates,
        ),
    ]
