# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("prompt_studio_core_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("prompt_profile_manager_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ToolStudioPrompt",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "prompt_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "prompt_key",
                    models.TextField(db_comment="Field to store the prompt key"),
                ),
                (
                    "enforce_type",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("Text", "Response sent as Text"),
                            ("number", "Response sent as number"),
                            ("email", "Response sent as email"),
                            ("date", "Response sent as date"),
                            ("boolean", "Response sent as boolean"),
                            ("json", "Response sent as json"),
                            ("table", "Response sent as table"),
                        ],
                        db_comment="Field to store the type in             which the response to be returned.",
                        default="Text",
                    ),
                ),
                (
                    "prompt",
                    models.TextField(blank=True, db_comment="Field to store the prompt"),
                ),
                ("sequence_number", models.IntegerField(blank=True, null=True)),
                (
                    "prompt_type",
                    models.TextField(
                        blank=True,
                        choices=[
                            ("PROMPT", "Response sent as Text"),
                            ("NOTES", "Response sent as float"),
                        ],
                        db_comment="Field to store the type of the input prompt",
                    ),
                ),
                ("output", models.TextField(blank=True)),
                (
                    "assert_prompt",
                    models.TextField(
                        blank=True,
                        db_comment="Field to store the asserted prompt",
                        null=True,
                    ),
                ),
                (
                    "assertion_failure_prompt",
                    models.TextField(
                        blank=True,
                        db_comment="Field to store the prompt key",
                        null=True,
                    ),
                ),
                ("is_assert", models.BooleanField(default=False)),
                ("active", models.BooleanField(default=True)),
                (
                    "output_metadata",
                    models.JSONField(
                        db_column="output_metadata",
                        db_comment="JSON adapter metadata for the FE to load the pagination",
                        default=dict,
                    ),
                ),
                ("evaluate", models.BooleanField(default=True)),
                ("eval_quality_faithfulness", models.BooleanField(default=True)),
                ("eval_quality_correctness", models.BooleanField(default=True)),
                ("eval_quality_relevance", models.BooleanField(default=True)),
                ("eval_security_pii", models.BooleanField(default=True)),
                ("eval_guidance_toxicity", models.BooleanField(default=True)),
                ("eval_guidance_completeness", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tool_studio_prompts_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tool_studio_prompts_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "profile_manager",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="tool_studio_prompts",
                        to="prompt_profile_manager_v2.profilemanager",
                    ),
                ),
                (
                    "tool_id",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="mapped_prompt",
                        to="prompt_studio_core_v2.customtool",
                    ),
                ),
            ],
            options={
                "verbose_name": "Tool Studio Prompt",
                "verbose_name_plural": "Tool Studio Prompts",
                "db_table": "tool_studio_prompt",
            },
        ),
        migrations.AddConstraint(
            model_name="toolstudioprompt",
            constraint=models.UniqueConstraint(
                fields=("prompt_key", "tool_id"), name="unique_prompt_key_tool_id_index"
            ),
        ),
    ]
