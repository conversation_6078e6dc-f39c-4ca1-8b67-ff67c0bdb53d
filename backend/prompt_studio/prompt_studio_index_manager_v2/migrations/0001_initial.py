# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("prompt_studio_document_manager_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("prompt_profile_manager_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="IndexManager",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "index_manager_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "raw_index_id",
                    models.CharField(
                        blank=True,
                        db_comment="Field to store the raw index id",
                        editable=False,
                        null=True,
                    ),
                ),
                (
                    "summarize_index_id",
                    models.Char<PERSON>ield(
                        blank=True,
                        db_comment="Field to store the summarize index id",
                        editable=False,
                        null=True,
                    ),
                ),
                (
                    "index_ids_history",
                    models.JSONField(db_comment="List of index ids", default=list),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="index_managers_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "document_manager",
                    models.ForeignKey(
                        editable=False,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="index_managers",
                        to="prompt_studio_document_manager_v2.documentmanager",
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="index_managers_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "profile_manager",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="index_managers",
                        to="prompt_profile_manager_v2.profilemanager",
                    ),
                ),
            ],
            options={
                "verbose_name": "Index Manager",
                "verbose_name_plural": "Index Managers",
                "db_table": "index_manager",
            },
        ),
        migrations.AddConstraint(
            model_name="indexmanager",
            constraint=models.UniqueConstraint(
                fields=("document_manager", "profile_manager"),
                name="unique_document_manager_profile_manager_index",
            ),
        ),
    ]
