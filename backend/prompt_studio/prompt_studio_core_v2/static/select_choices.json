{"combined_output": {"JSON": "JSON", "YAML": "YAML"}, "choose_llm": {"AZURE": "Azure OpenAI"}, "output_type": {"string": "text", "number": "number", "email": "email", "date": "date", "boolean": "boolean", "json": "json", "line_item": "line-item", "table": "table"}, "output_processing": {"DEFAULT": "<PERSON><PERSON><PERSON>"}, "embedding": {"azure_openai_embedding": "azure_openai_embedding", "openai_embedding": "openai_embedding"}, "retrieval_strategy": {"simple": "simple", "subquestion": "subquestion", "fusion": "fusion", "recursive": "recursive", "router": "router", "keyword_table": "keyword_table", "automerging": "automerging"}, "vector_store": {"Postgres pg_vector": "Postgres pg_vector", "qdrant": "qdrant"}}