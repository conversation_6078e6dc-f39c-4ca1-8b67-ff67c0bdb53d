# Generated by Django 4.2.1 on 2024-07-29 09:16

import uuid

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("adapter_processor_v2", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("account_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="CustomTool",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "tool_id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("tool_name", models.TextField()),
                ("description", models.TextField()),
                (
                    "author",
                    models.TextField(
                        db_comment="Specific to the user who created the tool."
                    ),
                ),
                (
                    "icon",
                    models.TextField(
                        blank=True,
                        db_comment="Field to store             icon url for the custom tool.",
                    ),
                ),
                (
                    "output",
                    models.TextField(
                        blank=True, db_comment="Field to store the output format type."
                    ),
                ),
                (
                    "log_id",
                    models.UUIDField(
                        db_comment="Field to store unique log_id for polling",
                        default=uuid.uuid4,
                    ),
                ),
                (
                    "summarize_context",
                    models.BooleanField(
                        db_comment="Flag to summarize content", default=False
                    ),
                ),
                (
                    "summarize_as_source",
                    models.BooleanField(
                        db_comment="Flag to use summarized content as source",
                        default=False,
                    ),
                ),
                (
                    "summarize_prompt",
                    models.TextField(
                        blank=True, db_comment="Field to store the summarize prompt"
                    ),
                ),
                (
                    "preamble",
                    models.TextField(
                        blank=True,
                        db_comment="Preamble to the prompts",
                        default="Your ability to extract and summarize this context accurately is essential for effective analysis. Pay close attention to the context's language, structure, and any cross-references to ensure a comprehensive and precise extraction of information. Do not use prior knowledge or information from outside the context to answer the questions. Only use the information provided in the context to answer the questions.",
                    ),
                ),
                (
                    "postamble",
                    models.TextField(
                        blank=True,
                        db_comment="Appended as postable to prompts.",
                        default="Do not include any explanation in the reply. Only include the extracted information in the reply.",
                    ),
                ),
                (
                    "prompt_grammer",
                    models.JSONField(
                        blank=True,
                        db_comment="Synonymous words used in prompt",
                        null=True,
                    ),
                ),
                (
                    "exclude_failed",
                    models.BooleanField(
                        db_comment="Flag to make the answer null if it is incorrect",
                        default=True,
                    ),
                ),
                (
                    "single_pass_extraction_mode",
                    models.BooleanField(
                        db_comment="Flag to enable or disable single pass extraction mode",
                        default=False,
                    ),
                ),
                (
                    "enable_challenge",
                    models.BooleanField(
                        db_comment="Flag to enable or disable challenge", default=False
                    ),
                ),
                (
                    "enable_highlight",
                    models.BooleanField(
                        db_comment="Flag to enable or disable document highlighting",
                        default=False,
                    ),
                ),
                (
                    "challenge_llm",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Field to store challenge llm",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="custom_tools_challenge",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="custom_tools_created",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "modified_by",
                    models.ForeignKey(
                        blank=True,
                        editable=False,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="custom_tools_modified",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "monitor_llm",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Field to store monitor llm",
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="custom_tools_monitor",
                        to="adapter_processor_v2.adapterinstance",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
                (
                    "shared_users",
                    models.ManyToManyField(
                        related_name="shared_custom_tools", to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
            options={
                "verbose_name": "Custom Tool",
                "verbose_name_plural": "Custom Tools",
                "db_table": "custom_tool",
            },
        ),
        migrations.AddConstraint(
            model_name="customtool",
            constraint=models.UniqueConstraint(
                fields=("tool_name", "organization"), name="unique_tool_name"
            ),
        ),
    ]
