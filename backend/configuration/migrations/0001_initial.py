# Generated by Django 4.2.1 on 2025-07-10 06:24

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("account_v2", "0002_user_auth_provider"),
    ]

    operations = [
        migrations.CreateModel(
            name="Configuration",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        help_text="Configuration key - must be a valid ConfigKey enum value",
                        max_length=100,
                    ),
                ),
                ("value", models.TextField()),
                ("enabled", models.BooleanField(default=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="organization_configuration",
                        to="account_v2.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Configuration",
                "verbose_name_plural": "Configurations",
                "db_table": "configuration",
            },
        ),
        migrations.AddConstraint(
            model_name="configuration",
            constraint=models.UniqueConstraint(
                fields=("organization", "key"), name="unique_organization_key"
            ),
        ),
    ]
