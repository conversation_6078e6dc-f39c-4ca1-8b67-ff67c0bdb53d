# Generated by Django 4.2.1 on 2025-01-20 06:07

from django.db import migrations, models


def convert_empty_to_null(apps, schema_editor):
    usage = apps.get_model("usage_v2", "Usage")
    usage.objects.filter(run_id="").update(run_id=None)


class Migration(migrations.Migration):
    dependencies = [
        ("usage_v2", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(
            convert_empty_to_null, reverse_code=migrations.RunPython.noop
        ),
        migrations.AlterField(
            model_name="usage",
            name="run_id",
            field=models.UUIDField(
                blank=True, db_comment="Identifier for the run", null=True
            ),
        ),
    ]
