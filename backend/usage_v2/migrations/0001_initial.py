# Generated by Django 4.2.1 on 2024-09-25 09:55

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("account_v2", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Usage",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("modified_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        db_comment="Primary key for the usage entry, automatically generated UUID",
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "workflow_id",
                    models.CharField(
                        blank=True,
                        db_comment="Identifier for the workflow",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "execution_id",
                    models.Char<PERSON><PERSON>(
                        blank=True,
                        db_comment="Identifier for the execution instance",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "adapter_instance_id",
                    models.<PERSON>r<PERSON><PERSON>(
                        db_comment="Identifier for the adapter instance", max_length=255
                    ),
                ),
                (
                    "run_id",
                    models.CharField(
                        blank=True,
                        db_comment="Identifier for the run",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "usage_type",
                    models.CharField(
                        choices=[
                            ("llm", "LLM Usage"),
                            ("embedding", "Embedding Usage"),
                        ],
                        db_comment="Type of usage, either 'llm' or 'embedding'",
                        max_length=255,
                    ),
                ),
                (
                    "llm_usage_reason",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("extraction", "Extraction"),
                            ("challenge", "Challenge"),
                            ("summarize", "Summarize"),
                        ],
                        db_comment="Reason for LLM usage. Empty if usage_type is 'embedding'. ",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "model_name",
                    models.CharField(db_comment="Name of the model used", max_length=255),
                ),
                (
                    "embedding_tokens",
                    models.IntegerField(db_comment="Number of tokens used for embedding"),
                ),
                (
                    "prompt_tokens",
                    models.IntegerField(
                        db_comment="Number of tokens used for the prompt"
                    ),
                ),
                (
                    "completion_tokens",
                    models.IntegerField(
                        db_comment="Number of tokens used for the completion"
                    ),
                ),
                (
                    "total_tokens",
                    models.IntegerField(db_comment="Total number of tokens used"),
                ),
                (
                    "cost_in_dollars",
                    models.FloatField(db_comment="Total number of tokens used"),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        db_comment="Foreign key reference to the Organization model.",
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="account_v2.organization",
                    ),
                ),
            ],
            options={
                "db_table": "usage",
                "indexes": [
                    models.Index(fields=["run_id"], name="usage_run_id_c84096_idx")
                ],
            },
        ),
    ]
