# Generated by Django 4.2.1 on 2024-08-05 06:10

import uuid

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PageUsage",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        db_comment="Primary key for the usage entry, automatically generated UUID",
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("organization_id", models.<PERSON>r<PERSON><PERSON>(default="mock_org")),
                (
                    "file_name",
                    models.Char<PERSON>ield(
                        blank=True, db_comment="Name of the file", max_length=255
                    ),
                ),
                (
                    "file_type",
                    models.Char<PERSON>ield(
                        blank=True, db_comment="Mime type of file", max_length=128
                    ),
                ),
                (
                    "run_id",
                    models.CharField(
                        blank=True, db_comment="Identifier for the run", max_length=255
                    ),
                ),
                (
                    "pages_processed",
                    models.Integer<PERSON>ield(
                        db_comment="Total number of pages in the document"
                    ),
                ),
                (
                    "file_size",
                    models.<PERSON>Inte<PERSON><PERSON>ield(db_comment="Size of the the file"),
                ),
                ("created_at", models.DateTime<PERSON>ield(auto_now_add=True)),
            ],
            options={
                "db_table": "page_usage",
                "indexes": [
                    models.Index(
                        fields=["organization_id"], name="page_usage_organiz_b56749_idx"
                    )
                ],
            },
        ),
    ]
