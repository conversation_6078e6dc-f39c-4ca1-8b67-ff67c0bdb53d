"""Django settings for backend project.

Generated by 'django-admin startproject' using Django 4.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import logging
import os
from pathlib import Path
from urllib.parse import urlparse

import httpx
from dotenv import find_dotenv, load_dotenv
from utils.common_utils import CommonUtils

missing_settings = []


def get_required_setting(setting_key: str, default: str | None = None) -> str | None:
    """Get the value of an environment variable specified by the given key. Add
    missing keys to `missing_settings` so that exception can be raised at the
    end.

    Args:
        key (str): The key of the environment variable
        default (Optional[str], optional): Default value to return incase of
                                           env not found. Defaults to None.

    Returns:
        Optional[str]: The value of the environment variable if found,
                       otherwise the default value.
    """
    data = os.environ.get(setting_key, default)
    if not data:
        missing_settings.append(setting_key)
    return data


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Load default log from env
DEFAULT_LOG_LEVEL = os.environ.get("DEFAULT_LOG_LEVEL", "INFO")

# Celery Broker Configuration
CELERY_BROKER_BASE_URL = get_required_setting("CELERY_BROKER_BASE_URL")
CELERY_BROKER_USER = get_required_setting("CELERY_BROKER_USER")
CELERY_BROKER_PASS = get_required_setting("CELERY_BROKER_PASS")
CELERY_BROKER_URL = str(
    httpx.URL(CELERY_BROKER_BASE_URL).copy_with(
        username=CELERY_BROKER_USER, password=CELERY_BROKER_PASS
    )
)

ENV_FILE = find_dotenv()
if ENV_FILE:
    load_dotenv(ENV_FILE)

# Loading environment variables

WORKFLOW_ACTION_EXPIRATION_TIME_IN_SECOND = os.environ.get(
    "WORKFLOW_ACTION_EXPIRATION_TIME_IN_SECOND", 10800
)
WEB_APP_ORIGIN_URL = os.environ.get("WEB_APP_ORIGIN_URL", "http://localhost:3000")
parsed_url = urlparse(WEB_APP_ORIGIN_URL)
WEB_APP_ORIGIN_URL_WITH_WILD_CARD = f"{parsed_url.scheme}://*.{parsed_url.netloc}"
CORS_ALLOWED_ORIGINS = [WEB_APP_ORIGIN_URL]

DJANGO_APP_BACKEND_URL = os.environ.get("DJANGO_APP_BACKEND_URL", "http://localhost:8000")
INTERNAL_SERVICE_API_KEY = os.environ.get("INTERNAL_SERVICE_API_KEY")

GOOGLE_STORAGE_ACCESS_KEY_ID = os.environ.get("GOOGLE_STORAGE_ACCESS_KEY_ID")
GOOGLE_STORAGE_SECRET_ACCESS_KEY = os.environ.get("GOOGLE_STORAGE_SECRET_ACCESS_KEY")
UNSTRACT_FREE_STORAGE_BUCKET_NAME = os.environ.get(
    "UNSTRACT_FREE_STORAGE_BUCKET_NAME", "pandora-user-storage"
)
GOOGLE_STORAGE_BASE_URL = os.environ.get("GOOGLE_STORAGE_BASE_URL")
REDIS_USER = os.environ.get("REDIS_USER", "default")
REDIS_PASSWORD = os.environ.get("REDIS_PASSWORD", "")
REDIS_HOST = os.environ.get("REDIS_HOST", "localhost")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")
REDIS_DB = os.environ.get("REDIS_DB", "")
SESSION_EXPIRATION_TIME_IN_SECOND = os.environ.get(
    "SESSION_EXPIRATION_TIME_IN_SECOND", 3600
)
SESSION_COOKIE_SECURE = CommonUtils.str_to_bool(
    os.environ.get("SESSION_COOKIE_SECURE", "False")
)
CSRF_COOKIE_SECURE = CommonUtils.str_to_bool(
    os.environ.get("CSRF_COOKIE_SECURE", "False")
)

PATH_PREFIX = os.environ.get("PATH_PREFIX", "api/v1").strip("/")
# Resetting the path prefix will require reconfiguring all existing deployed APIs
API_DEPLOYMENT_PATH_PREFIX = os.environ.get(
    "API_DEPLOYMENT_PATH_PREFIX", "deployment"
).strip("/")

DB_NAME = os.environ.get("DB_NAME", "unstract_db")
DB_USER = os.environ.get("DB_USER", "unstract_dev")
DB_HOST = os.environ.get("DB_HOST", "backend-db-1")
DB_PASSWORD = os.environ.get("DB_PASSWORD", "unstract_pass")
DB_PORT = os.environ.get("DB_PORT", 5432)
DB_SCHEMA = os.environ.get("DB_SCHEMA", "unstract")

# Celery Backend Database Name (falls back to main DB when unset or empty)
CELERY_BACKEND_DB_NAME = os.environ.get("CELERY_BACKEND_DB_NAME") or DB_NAME
DEFAULT_ORGANIZATION = "default_org"
FLIPT_BASE_URL = os.environ.get("FLIPT_BASE_URL", "http://localhost:9005")
PLATFORM_HOST = os.environ.get("PLATFORM_SERVICE_HOST", "http://localhost")
PLATFORM_PORT = os.environ.get("PLATFORM_SERVICE_PORT", 3001)
PROMPT_HOST = os.environ.get("PROMPT_HOST", "http://localhost")
PROMPT_PORT = os.environ.get("PROMPT_PORT", 3003)
PROMPT_STUDIO_FILE_PATH = os.environ.get(
    "PROMPT_STUDIO_FILE_PATH", "/app/prompt-studio-data"
)
X2TEXT_HOST = os.environ.get("X2TEXT_HOST", "http://localhost")
X2TEXT_PORT = os.environ.get("X2TEXT_PORT", 3004)
STRUCTURE_TOOL_IMAGE_URL = get_required_setting("STRUCTURE_TOOL_IMAGE_URL")
STRUCTURE_TOOL_IMAGE_NAME = get_required_setting("STRUCTURE_TOOL_IMAGE_NAME")
STRUCTURE_TOOL_IMAGE_TAG = get_required_setting("STRUCTURE_TOOL_IMAGE_TAG")
CACHE_TTL_SEC = os.environ.get("CACHE_TTL_SEC", 10800)

DEFAULT_AUTH_USERNAME = os.environ.get("DEFAULT_AUTH_USERNAME", "unstract")
DEFAULT_AUTH_PASSWORD = os.environ.get("DEFAULT_AUTH_PASSWORD", "unstract")
SYSTEM_ADMIN_USERNAME = get_required_setting("SYSTEM_ADMIN_USERNAME")
SYSTEM_ADMIN_PASSWORD = get_required_setting("SYSTEM_ADMIN_PASSWORD")
SYSTEM_ADMIN_EMAIL = get_required_setting("SYSTEM_ADMIN_EMAIL")
SESSION_COOKIE_AGE = int(get_required_setting("SESSION_COOKIE_AGE", "86400"))
ENABLE_LOG_HISTORY = get_required_setting("ENABLE_LOG_HISTORY")
LOG_HISTORY_CONSUMER_INTERVAL = int(
    get_required_setting("LOG_HISTORY_CONSUMER_INTERVAL", "60")
)
LOGS_BATCH_LIMIT = int(get_required_setting("LOGS_BATCH_LIMIT", "30"))
LOGS_EXPIRATION_TIME_IN_SECOND = int(
    get_required_setting("LOGS_EXPIRATION_TIME_IN_SECOND", "86400")
)
EXECUTION_RESULT_TTL_SECONDS = int(
    os.environ.get("EXECUTION_RESULT_TTL_SECONDS", 10800)
)  # 3 hours
EXECUTION_CACHE_TTL_SECONDS = int(
    os.environ.get("EXECUTION_CACHE_TTL_SECONDS", 10800)
)  # 3 hours
FILE_EXECUTION_TRACKER_TTL_IN_SECOND = int(
    os.environ.get("FILE_EXECUTION_TRACKER_TTL_IN_SECOND", 60 * 60 * 5)
)
FILE_EXECUTION_TRACKER_COMPLETED_TTL_IN_SECOND = int(
    os.environ.get("FILE_EXECUTION_TRACKER_COMPLETED_TTL_IN_SECOND", 60 * 10)
)  # 10 minutes

INSTANT_WF_POLLING_TIMEOUT = int(
    os.environ.get("INSTANT_WF_POLLING_TIMEOUT", "300")
)  # 5 minutes

# ETL Pipeline minimum schedule interval (in seconds)
# Default: 1800 seconds (30 minutes)
MIN_SCHEDULE_INTERVAL_SECONDS = int(os.environ.get("MIN_SCHEDULE_INTERVAL_SECONDS", 1800))

# File processing batches
MAX_PARALLEL_FILE_BATCHES = int(os.environ.get("MAX_PARALLEL_FILE_BATCHES", 1))
# Upper limit for batch validation
MAX_PARALLEL_FILE_BATCHES_MAX_VALUE = int(
    os.environ.get("MAX_PARALLEL_FILE_BATCHES_MAX_VALUE", 100)
)

CELERY_RESULT_CHORD_RETRY_INTERVAL = int(
    os.environ.get("CELERY_RESULT_CHORD_RETRY_INTERVAL", "3")
)

INDEXING_FLAG_TTL = int(get_required_setting("INDEXING_FLAG_TTL"))
NOTIFICATION_TIMEOUT = int(get_required_setting("NOTIFICATION_TIMEOUT", "5"))
ATOMIC_REQUESTS = CommonUtils.str_to_bool(
    os.environ.get("DJANGO_ATOMIC_REQUESTS", "False")
)
# Flag to Enable django admin
ADMIN_ENABLED = False

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = get_required_setting("DJANGO_SECRET_KEY")
ENCRYPTION_KEY = get_required_setting("ENCRYPTION_KEY")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]
CSRF_TRUSTED_ORIGINS = [WEB_APP_ORIGIN_URL, WEB_APP_ORIGIN_URL_WITH_WILD_CARD]
CORS_ALLOW_ALL_ORIGINS = False

# Request ID middleware settings
LOG_REQUEST_ID_HEADER = "X-Request-ID"
REQUEST_ID_RESPONSE_HEADER = "X-Request-ID"
GENERATE_REQUEST_ID_IF_NOT_IN_HEADER = True
NO_REQUEST_ID = "-"


class OTelFieldFilter(logging.Filter):
    def filter(self, record):
        for attr in ["otelTraceID", "otelSpanID"]:
            if not hasattr(record, attr):
                setattr(record, attr, "-")
        return True


# Logging configuration
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "request_id": {"()": "log_request_id.filters.RequestIDFilter"},
        "otel_ids": {"()": "backend.settings.base.OTelFieldFilter"},
    },
    "formatters": {
        "enriched": {
            "format": (
                "%(levelname)s : [%(asctime)s]"
                "{module:%(module)s process:%(process)d "
                "thread:%(thread)d request_id:%(request_id)s "
                "trace_id:%(otelTraceID)s span_id:%(otelSpanID)s} :- %(message)s"
            ),
        },
        "verbose": {
            "format": "[%(asctime)s] %(levelname)s %(name)s: %(message)s",
            "datefmt": "%d/%b/%Y %H:%M:%S",
        },
        "simple": {
            "format": "{levelname} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "console": {
            "level": DEFAULT_LOG_LEVEL,  # Set the desired logging level here
            "class": "logging.StreamHandler",
            "filters": ["request_id", "otel_ids"],
            "formatter": "enriched",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": DEFAULT_LOG_LEVEL,
        # Set the desired logging level here as well
    },
}

SHARED_APPS = (
    # Multitenancy
    # "django_tenants",
    "corsheaders",
    # For the organization model
    "account_v2",
    "account_usage",
    # Django apps should go below this line
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.admindocs",
    "django_filters",
    # Third party apps should go below this line,
    "rest_framework",
    # Connector OAuth
    # "connector_auth",
    "social_django",
    # Doc generator
    "drf_yasg",
    "docs",
    # Plugins
    "plugins",
    "feature_flag",
    "django_celery_beat",
    # For additional helper commands
    "commands",
    # health checks
    "health",
    "migrating.v2",
    "connector_auth_v2",
    "tenant_account_v2",
    "connector_v2",
    "adapter_processor_v2",
    "file_management",
    "workflow_manager.file_execution",
    "workflow_manager.endpoint_v2",
    "workflow_manager.workflow_v2",
    "workflow_manager.execution",
    "tool_instance_v2",
    "pipeline_v2",
    "platform_settings_v2",
    "api_v2",
    "usage_v2",
    "notification_v2",
    "prompt_studio.prompt_profile_manager_v2",
    "prompt_studio.prompt_studio_v2",
    "prompt_studio.prompt_studio_core_v2",
    "prompt_studio.prompt_studio_registry_v2",
    "prompt_studio.prompt_studio_output_manager_v2",
    "prompt_studio.prompt_studio_document_manager_v2",
    "prompt_studio.prompt_studio_index_manager_v2",
    "tags",
    "configuration",
)
TENANT_APPS = []

INSTALLED_APPS = list(SHARED_APPS) + [
    app for app in TENANT_APPS if app not in SHARED_APPS
]
DEFAULT_MODEL_BACKEND = "django.contrib.auth.backends.ModelBackend"
GOOGLE_MODEL_BACKEND = "social_core.backends.google.GoogleOAuth2"

AUTHENTICATION_BACKENDS = (
    DEFAULT_MODEL_BACKEND,
    GOOGLE_MODEL_BACKEND,
)

PUBLIC_ORG_ID = "public"

# Middleware Configuration
TENANT_MIDDLEWARE = "middleware.organization_middleware.OrganizationMiddleware"
CUSTOM_AUTH_MIDDLEWARE = "account_v2.custom_auth_middleware.CustomAuthMiddleware"

# Pipeline Functions
SOCIAL_AUTH_PIPELINE_USER_AUTH = "connector_auth_v2.pipeline.common.check_user_exists"
SOCIAL_AUTH_PIPELINE_CACHE_CRED = "connector_auth_v2.pipeline.common.cache_oauth_creds"

# Routing Configuration
ROOT_URLCONF = "backend.base_urls"

# DB Configuration
DB_ENGINE = "backend.custom_db"

# Models
AUTH_USER_MODEL = "account_v2.User"

# Social Authentication
SOCIAL_AUTH_USER_MODEL = "account_v2.User"
SOCIAL_AUTH_STORAGE = "connector_auth_v2.models.ConnectorDjangoStorage"

# Namespaces
SOCIAL_AUTH_URL_NAMESPACE = "public:social"
LOGIN_CALLBACK_URL_NAMESPACE = "public:callback"
DATABASES = {
    "default": {
        "ENGINE": DB_ENGINE,
        "NAME": f"{DB_NAME}",
        "USER": f"{DB_USER}",
        "HOST": f"{DB_HOST}",
        "PASSWORD": f"{DB_PASSWORD}",
        "PORT": f"{DB_PORT}",
        "ATOMIC_REQUESTS": ATOMIC_REQUESTS,
        "OPTIONS": {
            "application_name": os.environ.get("APPLICATION_NAME", ""),
        },
    }
}

MIDDLEWARE = [
    "middleware.request_id.CustomRequestIDMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    TENANT_MIDDLEWARE,
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    CUSTOM_AUTH_MIDDLEWARE,
    "middleware.exception.ExceptionLoggingMiddleware",
    "social_django.middleware.SocialAuthExceptionMiddleware",
    "middleware.remove_allow_header.RemoveAllowHeaderMiddleware",
    "middleware.cache_control.CacheControlMiddleware",
]

TENANT_SUBFOLDER_PREFIX = f"{PATH_PREFIX}/unstract"
SHOW_PUBLIC_IF_NO_TENANT_FOUND = True

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "backend.wsgi.application"

# SocketIO connection manager
SOCKET_IO_MANAGER_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}"

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{REDIS_HOST}:{REDIS_PORT}",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "SERIALIZER": "django_redis.serializers.json.JSONSerializer",
            "DB": REDIS_DB,
            "USERNAME": REDIS_USER,
            "PASSWORD": REDIS_PASSWORD,
        },
        "KEY_FUNCTION": "utils.redis_cache.custom_key_function",
    }
}

SESSION_ENGINE = "django.contrib.sessions.backends.cached_db"

RQ_QUEUES = {
    "default": {"USE_REDIS_CACHE": "default"},
}

# Feature Flag
FEATURE_FLAG_SERVICE_URL = {"evaluate": f"{FLIPT_BASE_URL}/api/v1/flags/evaluate/"}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation."
        "UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/
STATIC_URL = f"/{PATH_PREFIX}/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "DEFAULT_RENDERER_CLASSES": [
        "rest_framework.renderers.JSONRenderer",
    ],
    "DEFAULT_PERMISSION_CLASSES": [],  # TODO: Update once auth is figured
    "TEST_REQUEST_DEFAULT_FORMAT": "json",
    "EXCEPTION_HANDLER": "middleware.exception.drf_logging_exc_handler",
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.OrderingFilter",
    ],
    # For API versioning
    "DEFAULT_VERSIONING_CLASS": "rest_framework.versioning.URLPathVersioning",
    "DEFAULT_VERSION": "v1",
    "ALLOWED_VERSIONS": ["v1"],
    "VERSION_PARAM": "version",
}

# These paths will work without authentication
WHITELISTED_PATHS_LIST = [
    "/login",
    "/home",
    "/callback",
    "/favicon.ico",
    "/logout",
    "/signup",
    "/static",
]
WHITELISTED_PATHS = [f"/{PATH_PREFIX}{PATH}" for PATH in WHITELISTED_PATHS_LIST]
# White lists workflow-api-deployment path
WHITELISTED_PATHS.append(f"/{API_DEPLOYMENT_PATH_PREFIX}")

# Whitelisting health check API
WHITELISTED_PATHS.append("/health")

# These path will work without organization in request
ORGANIZATION_MIDDLEWARE_WHITELISTED_PATHS = []

# API Doc Generator Settings
# https://drf-yasg.readthedocs.io/en/stable/settings.html
REDOC_SETTINGS = {
    "PATH_IN_MIDDLE": True,
    "REQUIRED_PROPS_FIRST": True,
}

# Social Auth Settings
SOCIAL_AUTH_LOGIN_REDIRECT_URL = f"{WEB_APP_ORIGIN_URL}/oauth-status/?status=success"
SOCIAL_AUTH_LOGIN_ERROR_URL = f"{WEB_APP_ORIGIN_URL}/oauth-status/?status=error"
SOCIAL_AUTH_EXTRA_DATA_EXPIRATION_TIME_IN_SECOND = os.environ.get(
    "SOCIAL_AUTH_EXTRA_DATA_EXPIRATION_TIME_IN_SECOND", 3600
)
SOCIAL_AUTH_JSONFIELD_ENABLED = True
SOCIAL_AUTH_FIELDS_STORED_IN_SESSION = ["oauth-key", "connector-guid"]
SOCIAL_AUTH_TRAILING_SLASH = False

for key in [
    "GOOGLE_OAUTH2_KEY",
    "GOOGLE_OAUTH2_SECRET",
]:
    exec(f"SOCIAL_AUTH_{key} = os.environ.get('{key}')")

SOCIAL_AUTH_PIPELINE = (
    # Checks if user is authenticated
    SOCIAL_AUTH_PIPELINE_USER_AUTH,
    # Gets user details from provider
    "social_core.pipeline.social_auth.social_details",
    "social_core.pipeline.social_auth.social_uid",
    # Cache secrets and fields in redis
    SOCIAL_AUTH_PIPELINE_CACHE_CRED,
)

# Social Auth: Google OAuth2
# Default takes care of sign in flow which we don't need for connectors
SOCIAL_AUTH_GOOGLE_OAUTH2_IGNORE_DEFAULT_SCOPE = True
SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE = [
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/drive",
]
SOCIAL_AUTH_GOOGLE_OAUTH2_AUTH_EXTRA_ARGUMENTS = {
    "access_type": "offline",
    "include_granted_scopes": "true",
    "prompt": "consent",
}
SOCIAL_AUTH_GOOGLE_OAUTH2_USE_UNIQUE_USER_ID = True


# Always keep this line at the bottom of the file.
if missing_settings:
    ERROR_MESSAGE = "Below required settings are missing.\n" + ",\n".join(
        missing_settings
    )
    raise ValueError(ERROR_MESSAGE)

ENABLE_HIGHLIGHT_API_DEPLOYMENT = os.environ.get("ENABLE_HIGHLIGHT_API_DEPLOYMENT", False)
