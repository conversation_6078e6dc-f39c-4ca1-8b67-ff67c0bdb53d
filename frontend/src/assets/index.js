import { ReactComponent as <PERSON><PERSON><PERSON> } from "./sun-icon.svg";
import { ReactComponent as Moon<PERSON><PERSON> } from "./moon-icon.svg";
import { ReactComponent as Logo64 } from "./logo-64.svg";
import { ReactComponent as <PERSON><PERSON>24 } from "./logo-24.svg";
import { ReactComponent as Document } from "./document.svg";
import { ReactComponent as Folder } from "./folder.svg";
import { ReactComponent as BingAds } from "./BingAds.svg";
import { ReactComponent as ToolIcon } from "./tool.svg";
import { ReactComponent as InputPlaceholder } from "./input-placeholder.svg";
import { ReactComponent as OutputPlaceholder } from "./output-placeholder.svg";
import { ReactComponent as ToolIdeInputDocPlaceholder } from "./tool-ide-input-document-placeholder.svg";
import { ReactComponent as ToolIdePromptsPlaceholder } from "./tool-ide-prompts-placeholder.svg";
import { ReactComponent as UnstractLogo } from "./Unstract.svg";
import { ReactComponent as ListOfWfStepsPlaceholder } from "./list-of-wf-steps-placeholder.svg";
import { ReactComponent as ListOfToolsPlaceholder } from "./list-of-tools-placeholder.svg";
import { ReactComponent as ApiDeployments } from "./api-deployments.svg";
import { ReactComponent as Workflows } from "./Workflows.svg";
import { ReactComponent as Task } from "./task.svg";
import { ReactComponent as StepIcon } from "./steps.svg";
import { ReactComponent as CombinedOutputIcon } from "./combined-output.svg";
import { ReactComponent as EmptyPlaceholder } from "./empty.svg";
import { ReactComponent as Desktop } from "./desktop.svg";
import { ReactComponent as ReachOut } from "./reach-out.svg";
import { ReactComponent as RequireDemoIcon } from "./require-demo.svg";
import { ReactComponent as LearnMore } from "./learn-more.svg";
import { ReactComponent as UnstractBlackLogo } from "./UnstractLogoBlack.svg";
import { ReactComponent as SquareBg } from "./square-bg.svg";
import { ReactComponent as TrialDoc } from "./trial-doc.svg";
import { ReactComponent as TextExtractorIcon } from "./text-extractor.svg";
import { ReactComponent as OcrIcon } from "./ocr.svg";
import { ReactComponent as OrgAvatar } from "./org-selection-avatar.svg";
import { ReactComponent as OrgSelection } from "./org-selection.svg";
import { ReactComponent as RedGradCircle } from "./red-grad-circle.svg";
import { ReactComponent as YellowGradCircle } from "./yellow-grad-circle.svg";
import { ReactComponent as ExportToolIcon } from "./export-tool.svg";
import { ReactComponent as PlaceholderImg } from "./placeholder.svg";
import { ReactComponent as CustomToolIcon } from "./custom-tools-icon.svg";
import { ReactComponent as ETLIcon } from "./etl.svg";

export {
  SunIcon,
  MoonIcon,
  Logo64,
  Logo24,
  Document,
  Folder,
  BingAds,
  ToolIcon,
  InputPlaceholder,
  OutputPlaceholder,
  ToolIdeInputDocPlaceholder,
  ToolIdePromptsPlaceholder,
  UnstractLogo,
  ListOfWfStepsPlaceholder,
  ListOfToolsPlaceholder,
  ApiDeployments,
  Workflows,
  StepIcon,
  EmptyPlaceholder,
  CombinedOutputIcon,
  Desktop,
  ReachOut,
  RequireDemoIcon,
  LearnMore,
  UnstractBlackLogo,
  SquareBg,
  TrialDoc,
  TextExtractorIcon,
  OcrIcon,
  OrgAvatar,
  OrgSelection,
  RedGradCircle,
  YellowGradCircle,
  ExportToolIcon,
  PlaceholderImg,
  CustomToolIcon,
  ETLIcon,
  Task,
};
