/* WorkflowCard specific styles that extend the base styles */
.workflow-card .ds-set-card-row {
  margin-bottom: 0;
}

.workflow-card .ds-set-card-col1,
.workflow-card .ds-set-card-col2,
.workflow-card .ds-set-card-col3 {
  padding: 4px 0;
}

.workflow-card .ds-set-card-select {
  width: 100%;
}

/* Ensure the DsSettingsCard content fits well in the workflow card */
.workflow-card-content > div {
  width: 100%;
}

/* Connector type badge styling */
.connector-type-badge {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

/* Connector icon styling */
.workflow-card-number .connector-icon {
  display: block;
  object-fit: contain;
}
